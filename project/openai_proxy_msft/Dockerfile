# syntax=docker/dockerfile:1.7-labs
# This Dockerfile creates an optimized image for running the Bus Model Manager Streamlit application

# This version matches the one used by rcall in rcall/rcall/global_config.py.
# Some newer images have the wrong Python version, so we must be careful to
# match rcall.
ARG PYTHON_BASE_IMAGE="iridiumsdc.azurecr.io/rcall:crow-1229215"

FROM ${PYTHON_BASE_IMAGE}

# Set working directory
WORKDIR /root

# Install tini for proper signal handling
ENV TINI_VERSION v0.19.0
RUN ARCH=$(dpkg --print-architecture) \
    && if [ "$ARCH" = "amd64" ]; then TINI_ARCH="amd64"; elif [ "$ARCH" = "arm64" ]; then TINI_ARCH="arm64"; else TINI_ARCH="$ARCH"; fi \
    && wget -O /tini "https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini-${TINI_ARCH}" \
    && chmod +x /tini

RUN echo "python version: $(python --version)" \
    && echo "pip version: $(pip --version)" \
    && echo "python path: $(which python)"

ENV PATH="/root/.pyenv/versions/3.12.9/bin:$PATH"

# Copy both repositories from the build context
# glass repository contains the project files
COPY  glass/ /root/code/glass/
# torchflow-mirror repository contains install.py and oaipkg dependencies
COPY torchflow-mirror/ /root/code/openai/

# Set environment variables for oaipkg
ENV UV_BUILD_CONSTRAINT="/root/code/openai/oaipackaging/oaipkg/global_constraints.txt"
ENV UV_INDEX_URL="https://pypi.org/simple/"

# Pare down what oaipkg installs during bootstrap to the minimum necessary.
ENV OAIPKG_BOOTSTRAP_MINIMAL=1

# Disable oaipkg magic - nothing should get installed outside of pinned
# dependencies. Such installation would probably fail because haven't copied the
# entire repo.
ENV OAIPKG_DISABLE_META_MISSING=1

# Docker Desktop "amd64" CPU type is generic, but we need to download wheels
# which are only built for avx2. This is safe because wheels don't actually run
# in the build process, only when the image is deployed to a cluster.
ENV OAIPKG_CPU_ISALEVEL=avx2
ENV OAIPKG_INSTALL_CPU_OPTIMIZER_KERNELS=unsafe_skip
ENV OAIPKG_INSTALL_PROTON_WHEEL=unsafe_skip

ENV OAIPKG_VERBOSE=1

# Install openai_proxy_msft and its dependencies using oaipkg
# Set PYTHONPATH to include glass repository for package discovery
ENV OAIPKG_POLYREPO_ROOTS="/root/code/glass"
# ENV PYTHONPATH="/root/code/glass:$PYTHONPATH"

# copy wheels to the correct location indicated by:
# $shaHash = sha1($OAIPKG_ARTIFACTS_BASE/wheels/<oai_wheel_file_folder>/<oai_wheel_file_name>)
# target_path = $BUILDKITE_AGENT_CACHE_HOST_DIR/oaipkg_wheels_cache/<sha1/<oai_wheel_file_name>>/
# i.e. from /wheelcache/oaipkg_wheels_cache/<oai_wheel_file_folder>/<oai_wheel_file_name>
# to /oaiwheelcache/oaipkg_wheels_cache/$shaHash/<oai_wheel_file_name>
# For example:
# from /wheelcache/oaipkg_wheels_cache/multimodal-token/multimodal_token-1.0.0+git.taa93a97b6b.os.noble-cp311-cp311-linux_x86_64.whl
# to /oaiwheelcache/oaipkg_wheels_cache/041e8c51fd50ab31/multimodal_token-1.0.0+git.taa93a97b6b.os.noble-cp311-cp311-linux_x86_64.whl
ENV OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts
ENV BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache"
RUN --mount=type=bind,source=./orngoaiartifacts/wheels,target=/wheelcache/oaipkg_wheels_cache/ python /root/code/glass/project/openai_proxy_msft/copy_wheels.py

RUN --mount=type=secret,id=tokencache,dst=/root/.azure/msal_token_cache.json --mount=type=secret,id=profile,dst=/root/.azure/azureProfile.json \
    PIP_NO_CACHE_DIR=off UV_NO_CACHE=true BUILDKITE=1 OAIPKG_INSTALLER_BACKEND=uv \
    /root/code/openai/install.py openai_proxy_msft \
    && rm -rf /tmp/oaipkg

# Set environment variables for oaipkg and Streamlit
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Expose Streamlit port
EXPOSE 8500

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8500/ || exit 1

# Use tini as entrypoint for proper signal handling
ENTRYPOINT ["/tini", "--"]

# Start the Streamlit application
CMD ["proxy --background"]